import { Injectable, Ng<PERSON>one, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Observable, Subject, BehaviorSubject, fromEvent, EMPTY, timer } from 'rxjs';
import { map, catchError, retry, share, takeUntil, switchMap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { createLogger } from '../utils/logger';
import {
  EnhancedEventSourceInit,
  EnhancedEventSource,
  createSSEHeaders,
  supportsCustomHeaders,
  EventSourceFactory,
  createEventSource
} from '../types/eventsource-polyfill.types';

/**
 * SSE Event interface matching the expected event structure
 */
export interface SSEEvent {
  id?: string;
  event?: string;
  data: string;
  retry?: number;
}

/**
 * SSE Connection State for monitoring
 */
export interface SSEConnectionState {
  isConnected: boolean;
  isConnecting: boolean;
  lastConnectedTime: number;
  lastErrorTime: number;
  reconnectAttempts: number;
  totalEvents: number;
  connectionId: string;
}

/**
 * SSE Configuration options
 * ENHANCED: Now supports custom headers via EventSource polyfill and query parameters
 */
export interface SSEOptions {
  withCredentials?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  enableExponentialBackoff?: boolean;
  backoffFactor?: number;
  maxBackoffInterval?: number;
  enableHeartbeat?: boolean;
  heartbeatInterval?: number;
  lastEventId?: string; // For event ID-based checkpointing
  useHeadersForEventId?: boolean; // NEW: Use headers instead of URL parameters
  customHeaders?: Record<string, string>; // NEW: Additional custom headers
  queryParameters?: Record<string, string>; // NEW: Query parameters for URL optimization
  generationType?: 'initial-code-gen' | 'code-regen' | 'unknown'; // NEW: Generation type context
  enableSinceParameter?: boolean; // NEW: Enable since=0 parameter for initial generation
}

/**
 * Server-Sent Events (SSE) Service for Angular 19+
 *
 * Implements EventSource API with Angular best practices:
 * - Uses inject() function for dependency injection
 * - Uses Angular Signals for reactive state management
 * - Implements takeUntilDestroyed() for automatic cleanup
 * - Uses NgZone.run() for proper change detection
 * - Follows reactive patterns with observables
 *
 * Features:
 * - Automatic reconnection with exponential backoff
 * - Connection state monitoring
 * - Error handling and fallback mechanisms
 * - Network status awareness
 * - Proper lifecycle management
 */
@Injectable({
  providedIn: 'root'
})
export class SSEService {
  private readonly destroyRef = inject(DestroyRef);
  private readonly ngZone = inject(NgZone);
  private readonly logger = createLogger('SSEService');

  // Default configuration
  // ENHANCED: Now includes header-based options for EventSource polyfill and query parameters
  // CRITICAL FIX: Increased timeouts to prevent SSE connection errors
  private readonly DEFAULT_OPTIONS: Required<Omit<SSEOptions, 'lastEventId' | 'customHeaders' | 'queryParameters' | 'generationType'>> & {
    lastEventId?: string;
    customHeaders?: Record<string, string>;
    queryParameters?: Record<string, string>;
    generationType?: 'initial-code-gen' | 'code-regen' | 'unknown';
  } = {
    withCredentials: false,
    reconnectInterval: 5000, // INCREASED: 5 seconds between reconnects
    maxReconnectAttempts: 15, // INCREASED: More reconnect attempts
    enableExponentialBackoff: true,
    backoffFactor: 1.5,
    maxBackoffInterval: 60000, // INCREASED: 1 minute max backoff
    enableHeartbeat: true,
    heartbeatInterval: 60000, // INCREASED: 1 minute heartbeat interval
    useHeadersForEventId: true, // NEW: Default to using headers for better functionality
    enableSinceParameter: true, // NEW: Enable since=0 parameter optimization by default
    lastEventId: undefined,
    customHeaders: undefined,
    queryParameters: undefined,
    generationType: 'unknown'
  };

  // Connection state signals (Angular 19+ pattern)
  private readonly connectionState = signal<SSEConnectionState>({
    isConnected: false,
    isConnecting: false,
    lastConnectedTime: 0,
    lastErrorTime: 0,
    reconnectAttempts: 0,
    totalEvents: 0,
    connectionId: ''
  });

  // Private subjects for internal state management
  private eventSource: EventSource | null = null;
  private reconnectTimer: number | null = null;
  private heartbeatTimer: number | null = null;
  private destroy$ = new Subject<void>();
  private currentJobId: string | null = null;
  private currentOptions: Required<Omit<SSEOptions, 'lastEventId' | 'customHeaders' | 'queryParameters' | 'generationType'>> & {
    lastEventId?: string;
    customHeaders?: Record<string, string>;
    queryParameters?: Record<string, string>;
    generationType?: 'initial-code-gen' | 'code-regen' | 'unknown';
  } = this.DEFAULT_OPTIONS;

  // Event ID caching for checkpointing (Angular 19+ pattern)
  private readonly cachedEventId = signal<string | null>(null);
  private readonly regenerationSessionEventIds = new Map<string, string>();

  // ENHANCED: Flag to prevent reconnection on FAILED events
  private shouldPreventReconnection = false;

  // Public observables
  public readonly connectionState$ = this.connectionState.asReadonly();
  public readonly isConnected$ = new BehaviorSubject<boolean>(false);
  public readonly connectionError$ = new Subject<Event>();

  constructor() {
    // Setup automatic cleanup
    this.destroyRef.onDestroy(() => {
      this.cleanup();
    });

    // Monitor network status
    this.setupNetworkStatusMonitoring();

    // ENHANCED: Log polyfill status on service initialization
    this.logPolyfillStatus();
  }

  /**
   * Connect to SSE endpoint for project status updates
   * @param jobId The job ID to monitor
   * @param options Optional SSE configuration
   * @returns Observable of SSE events
   */
  connect(jobId: string, options?: Partial<SSEOptions>): Observable<SSEEvent> {
    if (!jobId) {
      this.logger.error('Cannot connect to SSE: missing job ID');
      return EMPTY;
    }

    // Merge options with defaults
    this.currentOptions = { ...this.DEFAULT_OPTIONS, ...options };
    this.currentJobId = jobId;

    // ENHANCED: Reset reconnection prevention flag for new connections
    this.resetReconnectionPrevention();

    // ENHANCED: Close existing connection if any with comprehensive cleanup
    if (this.isConnected() || this.eventSource) {
      this.logger.info('🧹 Disconnecting existing SSE connection before creating new one');
      this.disconnect();

      // Wait a brief moment to ensure cleanup is complete
      // This prevents race conditions with rapid connect/disconnect cycles
      if (this.eventSource) {
        this.logger.warn('⚠️ EventSource still exists after disconnect - forcing cleanup');
        this.cleanup();
      }
    }

    // Build SSE endpoint URL with query parameters
    const sseUrl = this.buildSSEUrl(jobId, this.currentOptions);
    this.logger.info(`🔌 Connecting to SSE endpoint: ${sseUrl}`);
    this.logger.info(`🔧 Current connection state:`, this.getConnectionState());
    this.logger.info(`🔧 EventSource support:`, typeof EventSource !== 'undefined');
    this.logger.info(`🔧 Generation type context:`, {
      generationType: this.currentOptions.generationType,
      enableSinceParameter: this.currentOptions.enableSinceParameter,
      hasQueryParams: !!this.currentOptions.queryParameters
    });

    const connection = this.createSSEConnection(sseUrl).pipe(
      share() // Share the connection among multiple subscribers
    );

    this.logger.info(`🚀 SSE Connection observable created for URL: ${sseUrl}`);
    return connection;
  }

  /**
   * Disconnect from SSE endpoint
   */
  disconnect(): void {
    this.logger.info('🔌 Disconnecting from SSE');

    this.destroy$.next();
    this.cleanup();

    // Update connection state
    this.updateConnectionState({
      isConnected: false,
      isConnecting: false
    });

    this.isConnected$.next(false);
  }

  /**
   * Get current connection state
   */
  getConnectionState(): SSEConnectionState {
    return this.connectionState();
  }

  /**
   * Check if currently connected
   */
  isConnected(): boolean {
    return this.connectionState().isConnected;
  }

  /**
   * Build the SSE endpoint URL with optional query parameters
   * ENHANCED: Supports query parameters for optimization (e.g., since=0 for initial generation)
   */
  private buildSSEUrl(jobId: string, options?: Partial<SSEOptions>): string {
    const baseUrl = environment.apiUrl;
    let url = `${baseUrl}/stream/project-status/${jobId}`;

    // Add query parameters if provided
    const queryParams = this.buildQueryParameters(options);
    if (queryParams) {
      url += `?${queryParams}`;
    }

    this.logger.info('🔗 Built SSE URL:', {
      jobId,
      baseUrl: url,
      generationType: options?.generationType,
      enableSinceParameter: options?.enableSinceParameter,
      queryParams,
      hasQueryParams: !!queryParams
    });

    return url;
  }

  /**
   * Build query parameters string from options
   * ENHANCED: Automatically adds since=0 for initial-code-gen events when enabled
   */
  private buildQueryParameters(options?: Partial<SSEOptions>): string {
    const params: Record<string, string> = {};

    this.logger.info('🔧 Building query parameters:', {
      hasOptions: !!options,
      generationType: options?.generationType,
      enableSinceParameter: options?.enableSinceParameter,
      customQueryParams: options?.queryParameters
    });

    // Add custom query parameters from options
    if (options?.queryParameters) {
      Object.assign(params, options.queryParameters);
      this.logger.info('📝 Added custom query parameters:', options.queryParameters);
    }

    // OPTIMIZATION: Add since parameter for both initial generation and regeneration
    if (this.shouldAddSinceParameter(options)) {
      const isInitialGeneration = options?.generationType === 'initial-code-gen';
      const isRegeneration = options?.generationType === 'code-regen';

      if (isInitialGeneration) {
        // For initial generation, start from the beginning
        params['since'] = '0';
        this.logger.info('🚀 Adding since=0 parameter for initial code generation optimization', {
          generationType: options?.generationType,
          enableSinceParameter: options?.enableSinceParameter
        });
      } else if (isRegeneration) {
        // For regeneration, use current timestamp to prevent replaying old events
        const currentTimestamp = Math.floor(Date.now() / 1000); // Unix timestamp in seconds
        params['since'] = currentTimestamp.toString();
        this.logger.info('🔄 Adding since=current_timestamp parameter for regeneration to prevent event replay', {
          generationType: options?.generationType,
          enableSinceParameter: options?.enableSinceParameter,
          sinceTimestamp: currentTimestamp,
          sinceValue: params['since']
        });
      }
    } else {
      this.logger.info('❌ NOT adding since parameter:', {
        generationType: options?.generationType,
        enableSinceParameter: options?.enableSinceParameter,
        reason: 'shouldAddSinceParameter returned false'
      });
    }

    // Convert to query string
    const queryString = Object.entries(params)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');

    this.logger.info('🔗 Final query string:', {
      queryString,
      paramsCount: Object.keys(params).length,
      params
    });

    return queryString;
  }

  /**
   * Determine if since parameter should be added and get its value
   * ENHANCED: Adds since=0 for initial-code-gen, since=current_timestamp for code-regen
   */
  private shouldAddSinceParameter(options?: Partial<SSEOptions>): boolean {
    // Check if since parameter optimization is enabled
    const sinceEnabled = options?.enableSinceParameter ?? this.DEFAULT_OPTIONS.enableSinceParameter;

    // Add for both initial generation and regeneration events
    const isInitialGeneration = options?.generationType === 'initial-code-gen';
    const isRegeneration = options?.generationType === 'code-regen';

    const shouldAdd = sinceEnabled && (isInitialGeneration || isRegeneration);

    this.logger.info('🔍 Evaluating since parameter addition:', {
      sinceEnabled,
      generationType: options?.generationType,
      isInitialGeneration,
      isRegeneration,
      shouldAdd,
      defaultSinceEnabled: this.DEFAULT_OPTIONS.enableSinceParameter,
      optionsSinceEnabled: options?.enableSinceParameter
    });

    return shouldAdd;
  }

  /**
   * Cache event ID for regeneration checkpointing
   * ENHANCED: Caches both globally and per session for cross-session compatibility
   * @param eventId The event ID to cache
   * @param sessionId Optional session ID for session-specific caching
   */
  cacheEventId(eventId: string, sessionId?: string): void {
    this.logger.info('💾 Caching event ID for checkpointing:', {
      eventId,
      sessionId,
      timestamp: new Date().toISOString()
    });

    // Update global cached event ID (used as fallback for first regeneration)
    this.cachedEventId.set(eventId);

    // Cache for specific session if provided
    if (sessionId) {
      this.regenerationSessionEventIds.set(sessionId, eventId);
    }

    // ENHANCED: Also cache as "latest" for cross-session compatibility
    // This allows first regeneration to use event ID from initial generation
    this.regenerationSessionEventIds.set('latest', eventId);
  }

  /**
   * Get cached event ID for regeneration checkpointing
   * ENHANCED: Uses session-specific, latest, or global cached event ID as fallback
   * @param sessionId Optional session ID for session-specific retrieval
   * @returns Cached event ID or null
   */
  getCachedEventId(sessionId?: string): string | null {
    // 1. Try session-specific cache first
    if (sessionId && this.regenerationSessionEventIds.has(sessionId)) {
      const sessionEventId = this.regenerationSessionEventIds.get(sessionId) || null;
      if (sessionEventId) {
        this.logger.debug('🔍 Using session-specific cached event ID:', {
          sessionId,
          eventId: sessionEventId
        });
        return sessionEventId;
      }
    }

    // 2. Try "latest" cache for cross-session compatibility (initial-gen -> first-regen)
    if (this.regenerationSessionEventIds.has('latest')) {
      const latestEventId = this.regenerationSessionEventIds.get('latest') || null;
      if (latestEventId) {
        this.logger.debug('🔍 Using latest cached event ID for cross-session compatibility:', {
          sessionId: sessionId || 'none',
          eventId: latestEventId
        });
        return latestEventId;
      }
    }

    // 3. Fall back to global cached event ID
    const globalEventId = this.cachedEventId();
    if (globalEventId) {
      this.logger.debug('🔍 Using global cached event ID as fallback:', {
        sessionId: sessionId || 'none',
        eventId: globalEventId
      });
    }

    return globalEventId;
  }

  /**
   * Clear cached event IDs
   * @param sessionId Optional session ID to clear specific session cache
   */
  clearCachedEventId(sessionId?: string): void {
    if (sessionId) {
      this.regenerationSessionEventIds.delete(sessionId);
      this.logger.info('🧹 Cleared cached event ID for session:', sessionId);
    } else {
      this.cachedEventId.set(null);
      this.regenerationSessionEventIds.clear();
      this.logger.info('🧹 Cleared all cached event IDs');
    }
  }

  /**
   * Append event ID to URL for checkpointing
   * @param url Base SSE URL
   * @param eventId Event ID to append
   * @returns URL with event ID parameter
   */
  private appendEventIdToUrl(url: string, eventId: string): string {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}lastEventId=${encodeURIComponent(eventId)}`;
  }

  /**
   * Create SSE connection with error handling and reconnection
   */
  private createSSEConnection(url: string): Observable<SSEEvent> {
    return new Observable<SSEEvent>(observer => {
      this.updateConnectionState({ isConnecting: true });

      try {
        // ENHANCED: Prepare EventSource configuration with polyfill support
        // Check if we should use headers (polyfill) or URL parameters (fallback)
        const useHeaders = this.currentOptions.useHeadersForEventId && this.isPolyfillAvailable();

        let finalUrl = url;
        let eventSourceConfig: EnhancedEventSourceInit = {
          withCredentials: this.currentOptions.withCredentials
        };

        // Configure last-event-id and custom headers
        if (this.currentOptions.lastEventId || this.currentOptions.customHeaders) {
          this.logger.info('🔄 Configuring SSE with cache ID and headers:', {
            lastEventId: this.currentOptions.lastEventId,
            useHeaders: useHeaders,
            hasCustomHeaders: !!this.currentOptions.customHeaders,
            polyfillAvailable: this.isPolyfillAvailable(),
            url: url
          });

          if (useHeaders) {
            // ENHANCED: Use headers with polyfill (preferred method)
            eventSourceConfig.headers = createSSEHeaders(
              this.currentOptions.lastEventId,
              this.currentOptions.customHeaders
            );
            this.logger.info('✅ Using header-based last-event-id:', {
              headers: eventSourceConfig.headers,
              lastEventId: this.currentOptions.lastEventId,
              headerName: 'last-event-id',
              method: 'HTTP Headers (Polyfill)'
            });
          } else {
            // FALLBACK: Use URL parameters for compatibility
            if (this.currentOptions.lastEventId) {
              finalUrl = this.appendEventIdToUrl(url, this.currentOptions.lastEventId);
              this.logger.warn('⚠️ Using URL parameter fallback for last-event-id (not SSE compliant):', {
                finalUrl: finalUrl,
                lastEventId: this.currentOptions.lastEventId,
                method: 'URL Parameters (Fallback)',
                reason: 'Polyfill not available or disabled'
              });
            }
          }
        }

        this.logger.info(`🔧 Creating EventSource for URL: ${finalUrl}`);
        this.logger.info(`🔧 EventSource configuration:`, {
          withCredentials: eventSourceConfig.withCredentials,
          hasHeaders: !!eventSourceConfig.headers,
          headers: eventSourceConfig.headers,
          usePolyfill: useHeaders
        });

        // Create EventSource connection using polyfill-aware factory
        this.eventSource = createEventSource(finalUrl, eventSourceConfig);

        this.logger.info(`✅ EventSource created successfully:`, {
          readyState: this.eventSource.readyState,
          url: this.eventSource.url,
          hasEventIdCheckpoint: !!this.currentOptions.lastEventId
        });

        const connectionId = this.generateConnectionId();
        this.updateConnectionState({ connectionId });

        // Handle connection open
        this.eventSource.onopen = (event) => {
          this.ngZone.run(() => {
            this.logger.info('✅ SSE connection established (onopen fired)');
            this.updateConnectionState({
              isConnected: true,
              isConnecting: false,
              lastConnectedTime: Date.now(),
              reconnectAttempts: 0
            });
            this.isConnected$.next(true);
            this.startHeartbeat();
          });
        };

        // Handle incoming messages
        this.eventSource.onmessage = (event) => {
          this.ngZone.run(() => {
            this.handleSSEMessage(event, observer, 'message');
          });
        };

        // Handle specific event types for generation events
        this.eventSource.addEventListener('initial-code-gen', (event) => {
          this.ngZone.run(() => {
            this.handleSSEMessage(event as MessageEvent, observer, 'initial-code-gen');
          });
        });

        this.eventSource.addEventListener('code-regen', (event) => {
          this.ngZone.run(() => {
            this.handleSSEMessage(event as MessageEvent, observer, 'code-regen');
          });
        });

        this.eventSource.addEventListener('update', (event) => {
          this.ngZone.run(() => {
            this.handleSSEMessage(event as MessageEvent, observer, 'update');
          });
        });

        // Handle other common event types
        this.eventSource.addEventListener('status', (event) => {
          this.ngZone.run(() => {
            this.handleSSEMessage(event as MessageEvent, observer, 'status');
          });
        });

        this.eventSource.addEventListener('progress', (event) => {
          this.ngZone.run(() => {
            this.handleSSEMessage(event as MessageEvent, observer, 'progress');
          });
        });

        // Handle specific event types
        this.eventSource.addEventListener('update', (event) => {
          this.ngZone.run(() => {
            this.handleSSEMessage(event as MessageEvent, observer);
          });
        });

        // Handle connection errors
        this.eventSource.onerror = (event) => {
          this.ngZone.run(() => {
            this.logger.error('❌ SSE connection error (onerror fired):', {
              readyState: this.eventSource?.readyState,
              url: this.eventSource?.url,
              event: event
            });
            this.handleSSEError(event, observer);
          });
        };

      } catch (error) {
        this.logger.error('❌ Failed to create SSE connection:', error);
        observer.error(error);
      }

      // Cleanup function
      return () => {
        this.cleanup();
      };
    });
  }

  /**
   * Handle incoming SSE messages with proper event type detection
   * ENHANCED: Properly handles different SSE event types for generation events
   * ENHANCED: Implements event ID caching for checkpointing
   * ENHANCED: Detects isFinal flag and closes initial-code-gen connections
   */
  private handleSSEMessage(event: MessageEvent, observer: any, eventType?: string): void {
    try {
      this.updateConnectionState(state => ({
        ...state,
        totalEvents: state.totalEvents + 1
      }));

      // CRITICAL FIX: Use the provided eventType or extract from event
      const actualEventType = eventType || (event as any).type || 'message';

      const sseEvent: SSEEvent = {
        id: (event as any).lastEventId,
        event: actualEventType,
        data: event.data
      };

      this.logger.info('📨 SSE event received with proper type:', {
        eventType: actualEventType,
        id: sseEvent.id,
        dataLength: event.data?.length || 0
      });

      // ENHANCED: Check for FAILED events and prevent reconnection
      this.checkForFailedEvent(sseEvent);

      // ENHANCED: Check for isFinal flag and close connection if needed
      this.checkForFinalEvent(sseEvent, observer);

      // ENHANCED: Extract and cache event ID for checkpointing
      this.extractAndCacheEventId(sseEvent);

      observer.next(sseEvent);

    } catch (error) {
      this.logger.error('❌ Error processing SSE message:', error);
      observer.error(error);
    }
  }

  /**
   * ENHANCED: Check for FAILED events and prevent reconnection
   * Detects FAILED status for BUILD/CODE_GENERATION/DEPLOY phases
   * @param sseEvent The SSE event to check
   */
  private checkForFailedEvent(sseEvent: SSEEvent): void {
    try {
      // Parse event data to check for FAILED status
      const eventData = JSON.parse(sseEvent.data);

      // Use the detectFailedEvent method to check if this is a FAILED event
      if (this.detectFailedEvent(eventData)) {
        this.logger.error('🚨 FAILED event detected in SSE stream - connection will be closed:', {
          eventId: sseEvent.id,
          eventType: sseEvent.event,
          progress: eventData.progress,
          status: eventData.status,
          errorMessage: eventData.errorMessage || eventData.log || 'Unknown error'
        });
      }
    } catch (error) {
      // If we can't parse the data, log but continue processing
      this.logger.debug('📨 Could not parse SSE event data for FAILED check:', {
        eventId: sseEvent.id,
        eventType: sseEvent.event,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Check for isFinal flag in initial-code-gen events and close connection if needed
   * ENHANCED: Implements automatic connection closure for completed initial generation
   * @param sseEvent The SSE event to check
   * @param observer The observer to complete if connection should be closed
   */
  private checkForFinalEvent(sseEvent: SSEEvent, observer: any): void {
    // Only check initial-code-gen events for isFinal flag
    if (sseEvent.event !== 'initial-code-gen') {
      return;
    }

    try {
      // Parse event data to check for isFinal flag
      const eventData = JSON.parse(sseEvent.data);
      const status = eventData.status;
      const progress = eventData.progress;
      const isFinal = eventData.isFinal;

      this.logger.info('🔍 Checking initial-code-gen event for completion:', {
        eventId: sseEvent.id,
        status,
        progress,
        isFinal,
        shouldClose: isFinal === true
      });

      // Check if this is a final event based on the specified conditions
      const isFinalEvent = isFinal === true && (
        // Both status and progress are FAILED
        (status === 'FAILED' && progress === 'FAILED') ||
        // Progress is DEPLOY and status is COMPLETED
        (progress === 'DEPLOY' && status === 'COMPLETED')
      );

      if (isFinalEvent) {
        this.logger.info('🏁 Final initial-code-gen event detected - closing SSE connection:', {
          eventId: sseEvent.id,
          status,
          progress,
          isFinal,
          reason: status === 'FAILED' ? 'Generation failed' : 'Generation completed successfully'
        });

        // Schedule connection closure after this event is processed
        setTimeout(() => {
          this.logger.info('🔌 Closing SSE connection due to isFinal flag');
          this.disconnect();
          observer.complete();
        }, 100); // Small delay to ensure event is processed first
      }

    } catch (error) {
      // If we can't parse the data, log but don't close connection
      this.logger.debug('📨 Could not parse initial-code-gen event data for isFinal check:', {
        eventId: sseEvent.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Extract and cache event ID from SSE event for checkpointing
   * ENHANCED: Caches event IDs from both initial-code-gen and code-regen events
   * @param sseEvent The SSE event to process
   */
  private extractAndCacheEventId(sseEvent: SSEEvent): void {
    if (!sseEvent.id) {
      return; // No event ID to cache
    }

    try {
      // Parse event data to check for completion status
      const eventData = JSON.parse(sseEvent.data);
      const status = eventData.status;
      const progress = eventData.progress;

      // ENHANCED: Cache event ID for both initial generation and regeneration events
      // Cache when: status is COMPLETED/FAILED AND progress is DEPLOY
      const shouldCacheEventId = (
        (sseEvent.event === 'initial-code-gen' || sseEvent.event === 'code-regen') &&
        ((progress === 'DEPLOY' && status === 'COMPLETED') || status === 'FAILED')
      );

      if (shouldCacheEventId) {
        this.logger.info('💾 Caching event ID for generation/regeneration checkpointing:', {
          eventId: sseEvent.id,
          status,
          progress,
          eventType: sseEvent.event,
          isInitialGeneration: sseEvent.event === 'initial-code-gen',
          isRegeneration: sseEvent.event === 'code-regen',
          timestamp: new Date().toISOString()
        });

        // Cache the event ID for future regeneration sessions
        this.cacheEventId(sseEvent.id);
      }

    } catch (error) {
      // If we can't parse the data, still log the event ID for debugging
      this.logger.debug('📨 SSE event ID received (data not parseable):', {
        eventId: sseEvent.id,
        eventType: sseEvent.event
      });
    }
  }

  /**
   * Handle SSE connection errors with enhanced error recovery
   * ENHANCED: Prevents reconnection when FAILED events are detected
   */
  private handleSSEError(event: Event, observer: any): void {
    const errorType = (event as any)?.type || 'unknown';
    const errorMessage = (event as any)?.message || 'SSE connection error';

    this.logger.error('❌ SSE connection error:', {
      type: errorType,
      message: errorMessage,
      readyState: this.eventSource?.readyState,
      url: this.eventSource?.url,
      shouldPreventReconnection: this.shouldPreventReconnection
    });

    this.updateConnectionState({
      isConnected: false,
      isConnecting: false,
      lastErrorTime: Date.now()
    });

    this.isConnected$.next(false);
    this.connectionError$.next(event);
    this.stopHeartbeat();

    // ENHANCED: Check if reconnection should be prevented due to FAILED events
    if (this.shouldPreventReconnection) {
      this.logger.info('🚫 Preventing reconnection due to FAILED event - closing connection permanently');
      this.cleanup();
      observer.error(new Error(`SSE connection closed due to FAILED event: ${errorMessage}`));
      return;
    }

    // Enhanced reconnection logic with better error categorization
    if (this.shouldAttemptReconnection()) {
      this.logger.info(`🔄 Scheduling SSE reconnection`);
      this.scheduleReconnection();
    } else {
      this.logger.error('❌ Max reconnection attempts reached, connection abandoned');
      observer.error(new Error(`SSE connection failed after ${this.currentOptions.maxReconnectAttempts} attempts: ${errorMessage}`));
    }
  }

  /**
   * Check if should attempt reconnection
   * ENHANCED: Also checks if reconnection is prevented due to FAILED events
   */
  private shouldAttemptReconnection(): boolean {
    if (this.shouldPreventReconnection) {
      return false;
    }
    const state = this.connectionState();
    return state.reconnectAttempts < this.currentOptions.maxReconnectAttempts;
  }

  /**
   * ENHANCED: Detect FAILED events and prevent reconnection
   * Checks if SSE event indicates a FAILED status for BUILD/CODE_GENERATION/DEPLOY
   */
  private detectFailedEvent(data: any): boolean {
    try {
      // Check if this is a FAILED event for critical phases
      const progress = data?.progress;
      const status = data?.status;

      if (status === 'FAILED' &&
          (progress === 'BUILD' || progress === 'CODE_GENERATION' || progress === 'DEPLOY')) {
        this.logger.error('🚨 FAILED event detected - preventing reconnection:', {
          progress,
          status,
          errorMessage: data?.errorMessage || data?.log || 'Unknown error',
          timestamp: new Date().toISOString()
        });

        // Set flag to prevent reconnection
        this.shouldPreventReconnection = true;
        return true;
      }

      return false;
    } catch (error) {
      this.logger.debug('Failed to parse SSE data for FAILED event detection:', error);
      return false;
    }
  }

  /**
   * ENHANCED: Reset reconnection prevention flag
   * Called when starting new connections to allow reconnection for new sessions
   */
  public resetReconnectionPrevention(): void {
    this.shouldPreventReconnection = false;
    this.logger.debug('🔄 Reconnection prevention flag reset');
  }

  /**
   * Schedule reconnection with exponential backoff
   */
  private scheduleReconnection(): void {
    const state = this.connectionState();
    let delay = this.currentOptions.reconnectInterval;

    if (this.currentOptions.enableExponentialBackoff) {
      delay = Math.min(
        delay * Math.pow(this.currentOptions.backoffFactor, state.reconnectAttempts),
        this.currentOptions.maxBackoffInterval
      );
    }

    this.logger.info(`🔄 Scheduling SSE reconnection in ${delay}ms (attempt ${state.reconnectAttempts + 1})`);

    this.reconnectTimer = window.setTimeout(() => {
      if (this.currentJobId) {
        this.updateConnectionState(state => ({
          ...state,
          reconnectAttempts: state.reconnectAttempts + 1
        }));

        // Reconnect with same job ID
        this.connect(this.currentJobId, this.currentOptions).pipe(
          takeUntil(this.destroy$)
        ).subscribe();
      }
    }, delay);
  }

  /**
   * Setup network status monitoring
   */
  private setupNetworkStatusMonitoring(): void {
    // Monitor online/offline events
    fromEvent(window, 'online').pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(() => {
      this.logger.info('🌐 Network back online, attempting SSE reconnection');
      if (this.currentJobId && !this.isConnected()) {
        this.connect(this.currentJobId, this.currentOptions).pipe(
          takeUntil(this.destroy$)
        ).subscribe();
      }
    });

    fromEvent(window, 'offline').pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(() => {
      this.logger.warn('🌐 Network offline, SSE connection will be affected');
    });
  }

  /**
   * Start heartbeat mechanism
   */
  private startHeartbeat(): void {
    if (!this.currentOptions.enableHeartbeat) return;

    this.stopHeartbeat();
    this.heartbeatTimer = window.setInterval(() => {
      // Check if connection is still alive
      if (this.eventSource?.readyState !== EventSource.OPEN) {
        this.logger.warn('💓 Heartbeat detected dead connection, triggering reconnection');
        this.handleSSEError(new Event('heartbeat-failure'), null);
      }
    }, this.currentOptions.heartbeatInterval);
  }

  /**
   * Stop heartbeat mechanism
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * Update connection state
   */
  private updateConnectionState(
    update: Partial<SSEConnectionState> | ((state: SSEConnectionState) => SSEConnectionState)
  ): void {
    if (typeof update === 'function') {
      this.connectionState.set(update(this.connectionState()));
    } else {
      this.connectionState.set({ ...this.connectionState(), ...update });
    }
  }

  /**
   * Generate unique connection ID
   */
  private generateConnectionId(): string {
    return `sse-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if EventSource polyfill is available and supports custom headers
   * ENHANCED: Improved polyfill detection for header-based last-event-id
   */
  private isPolyfillAvailable(): boolean {
    try {
      // Check if EventSource exists
      if (typeof EventSource === 'undefined') {
        this.logger.warn('⚠️ EventSource not available');
        return false;
      }

      // ENHANCED: Multiple detection methods for better reliability
      let hasPolyfillFeatures = false;

      // Method 1: Check for polyfill-specific properties
      try {
        // Create a minimal test EventSource to check capabilities
        const testConfig = { headers: { 'test': 'value' } };
        // const testEventSource = new EventSource('data:text/plain,test', testConfig);

        // If we can create EventSource with headers config without error, polyfill is likely active
        hasPolyfillFeatures = true;
        // testEventSource.close();

        this.logger.info('✅ EventSource polyfill detected via headers config test');
      } catch (headerTestError) {
        // Method 2: Check using supportsCustomHeaders function
        try {
          const basicEventSource = new EventSource('data:text/plain,test');
          hasPolyfillFeatures = supportsCustomHeaders(basicEventSource);
          basicEventSource.close();

          if (hasPolyfillFeatures) {
            this.logger.info('✅ EventSource polyfill detected via supportsCustomHeaders');
          }
        } catch (basicTestError) {
          // this.logger.warn('⚠️ Both polyfill detection methods failed:', {
          //   headerTestError: headerTestError.message,
          //   basicTestError: basicTestError.message
          // });
        }
      }

      // Method 3: Check for known polyfill indicators
      if (!hasPolyfillFeatures) {
        // Check if EventSource constructor has been enhanced
        const eventSourceString = EventSource.toString();
        const hasPolyfillIndicators =
          eventSourceString.includes('polyfill') ||
          eventSourceString.includes('headers') ||
          (EventSource as any).polyfill === true;

        if (hasPolyfillIndicators) {
          hasPolyfillFeatures = true;
          this.logger.info('✅ EventSource polyfill detected via constructor analysis');
        }
      }

      this.logger.info('🔍 EventSource polyfill detection result:', {
        hasEventSource: true,
        hasPolyfillFeatures: hasPolyfillFeatures,
        supportsHeaders: hasPolyfillFeatures,
        eventSourceConstructor: EventSource.name || 'EventSource',
        willUseHeaders: hasPolyfillFeatures
      });

      return hasPolyfillFeatures;
    } catch (error) {
      this.logger.warn('⚠️ Error detecting EventSource polyfill:', error);
      // ENHANCED: Default to true if we can't detect, let the polyfill handle it
      this.logger.info('🔄 Defaulting to header support - polyfill will handle compatibility');
      return true;
    }
  }

  /**
   * Log EventSource polyfill status for debugging
   * ENHANCED: Provides detailed information about polyfill capabilities
   */
  private logPolyfillStatus(): void {
    const polyfillAvailable = this.isPolyfillAvailable();
    const hasEventSource = typeof EventSource !== 'undefined';

    this.logger.info('🚀 SSE Service initialized with polyfill support:', {
      hasEventSource: hasEventSource,
      polyfillAvailable: polyfillAvailable,
      defaultUseHeaders: this.DEFAULT_OPTIONS.useHeadersForEventId,
      supportsCustomHeaders: polyfillAvailable,
      eventSourceType: hasEventSource ? EventSource.name || 'EventSource' : 'undefined',
      headerMethod: polyfillAvailable ? 'last-event-id (Headers)' : 'lastEventId (URL Parameters)',
      timestamp: new Date().toISOString()
    });

    if (!hasEventSource) {
      this.logger.error('❌ EventSource not supported in this environment');
    } else if (!polyfillAvailable) {
      this.logger.warn('⚠️ EventSource polyfill not detected - will fall back to URL parameters for last-event-id');
      this.logger.warn('💡 Consider ensuring event-source-polyfill is properly loaded for SSE specification compliance');
    } else {
      this.logger.info('✅ EventSource polyfill detected - header-based last-event-id available');
    }
  }

  /**
   * Force header-based mode for testing and debugging
   * ENHANCED: Allows forcing header mode regardless of polyfill detection
   */
  forceHeaderMode(enabled: boolean = true): void {
    this.DEFAULT_OPTIONS.useHeadersForEventId = enabled;
    this.logger.info(`🔧 Forced header mode ${enabled ? 'enabled' : 'disabled'}:`, {
      useHeadersForEventId: enabled,
      method: enabled ? 'last-event-id (Headers)' : 'lastEventId (URL Parameters)',
      note: 'This overrides automatic polyfill detection'
    });
  }

  /**
   * Get polyfill information for debugging
   * ENHANCED: Public method to check polyfill status with detailed info
   */
  getPolyfillInfo(): {
    hasEventSource: boolean;
    polyfillAvailable: boolean;
    supportsHeaders: boolean;
    defaultUseHeaders: boolean;
    headerMethod: string;
    isSSECompliant: boolean;
  } {
    const polyfillAvailable = this.isPolyfillAvailable();
    const useHeaders = this.DEFAULT_OPTIONS.useHeadersForEventId;

    return {
      hasEventSource: typeof EventSource !== 'undefined',
      polyfillAvailable: polyfillAvailable,
      supportsHeaders: polyfillAvailable,
      defaultUseHeaders: useHeaders,
      headerMethod: (polyfillAvailable && useHeaders) ? 'last-event-id (Headers)' : 'lastEventId (URL Parameters)',
      isSSECompliant: polyfillAvailable && useHeaders
    };
  }

  /**
   * Test header functionality for debugging
   * ENHANCED: Allows testing if headers are properly sent
   */
  testHeaderFunctionality(): Promise<{
    success: boolean;
    method: string;
    headers?: Record<string, string>;
    error?: string;
  }> {
    return new Promise((resolve) => {
      try {
        const testEventId = `test-${Date.now()}`;
        const testHeaders = createSSEHeaders(testEventId, { 'X-Test': 'header-test' });

        this.logger.info('🧪 Testing header functionality:', {
          testEventId,
          testHeaders,
          polyfillAvailable: this.isPolyfillAvailable()
        });

        // Try to create EventSource with headers
        const testEventSource = createEventSource('data:text/plain,test', {
          headers: testHeaders,
          withCredentials: false
        });

        testEventSource.onopen = () => {
          testEventSource.close();
          resolve({
            success: true,
            method: 'last-event-id (Headers)',
            headers: testHeaders
          });
        };

        testEventSource.onerror = (error) => {
          testEventSource.close();
          resolve({
            success: false,
            method: 'Headers failed',
            error: 'EventSource with headers failed to connect'
          });
        };

        // Timeout after 5 seconds
        setTimeout(() => {
          testEventSource.close();
          resolve({
            success: false,
            method: 'Headers timeout',
            error: 'Header test timed out'
          });
        }, 5000);

      } catch (error) {
        resolve({
          success: false,
          method: 'Headers error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });
  }

  /**
   * Cleanup resources
   */
  private cleanup(): void {
    // Close EventSource connection
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }

    // Clear timers
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    this.stopHeartbeat();
  }
}
